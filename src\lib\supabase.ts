import { createClient } from "@supabase/supabase-js";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing Supabase environment variables");
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    // Storage key for session persistence
    storageKey: "malombo-auth-token",
    // Custom storage implementation for better reliability
    storage: {
      getItem: (key: string) => {
        try {
          return localStorage.getItem(key);
        } catch (error) {
          console.warn("Error reading from localStorage:", error);
          return null;
        }
      },
      setItem: (key: string, value: string) => {
        try {
          localStorage.setItem(key, value);
        } catch (error) {
          console.warn("Error writing to localStorage:", error);
        }
      },
      removeItem: (key: string) => {
        try {
          localStorage.removeItem(key);
        } catch (error) {
          console.warn("Error removing from localStorage:", error);
        }
      },
    },
  },
});

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          role: "admin" | "staff";
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          role?: "admin" | "staff";
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          role?: "admin" | "staff";
          created_at?: string;
          updated_at?: string;
        };
      };
      activities: {
        Row: {
          id: string;
          title: string;
          category:
            | "game_drive"
            | "boat_safari"
            | "walk_safari"
            | "cultural_tour"
            | "fishing"
            | "village_tour"
            | "youth_program";
          description: string;
          duration: string;
          schedule?: string;
          pricing: string;
          inclusions: string[];
          images: string[];
          status: "draft" | "published" | "unpublished";
          featured: boolean;
          created_at: string;
          updated_at: string;
          created_by?: string;
          updated_by?: string;
        };
        Insert: {
          id?: string;
          title: string;
          category:
            | "game_drive"
            | "boat_safari"
            | "walk_safari"
            | "cultural_tour"
            | "fishing"
            | "village_tour"
            | "youth_program";
          description: string;
          duration: string;
          schedule?: string;
          pricing: string;
          inclusions?: string[];
          images?: string[];
          status?: "draft" | "published" | "unpublished";
          featured?: boolean;
          created_at?: string;
          updated_at?: string;
          created_by?: string;
          updated_by?: string;
        };
        Update: {
          id?: string;
          title?: string;
          category?:
            | "game_drive"
            | "boat_safari"
            | "walk_safari"
            | "cultural_tour"
            | "fishing"
            | "village_tour"
            | "youth_program";
          description?: string;
          duration?: string;
          schedule?: string;
          pricing?: string;
          inclusions?: string[];
          images?: string[];
          status?: "draft" | "published" | "unpublished";
          featured?: boolean;
          created_at?: string;
          updated_at?: string;
          created_by?: string;
          updated_by?: string;
        };
      };
      accommodations: {
        Row: {
          id: string;
          name: string;
          type: string;
          description: string;
          special_features?: string;
          amenities: string[];
          price_range: string;
          capacity: number;
          images: string[];
          status: "draft" | "published" | "unpublished";
          featured: boolean;
          created_at: string;
          updated_at: string;
          created_by?: string;
          updated_by?: string;
        };
        Insert: {
          id?: string;
          name: string;
          type: string;
          description: string;
          special_features?: string;
          amenities?: string[];
          price_range: string;
          capacity: number;
          images?: string[];
          status?: "draft" | "published" | "unpublished";
          featured?: boolean;
          created_at?: string;
          updated_at?: string;
          created_by?: string;
          updated_by?: string;
        };
        Update: {
          id?: string;
          name?: string;
          type?: string;
          description?: string;
          special_features?: string;
          amenities?: string[];
          price_range?: string;
          capacity?: number;
          images?: string[];
          status?: "draft" | "published" | "unpublished";
          featured?: boolean;
          created_at?: string;
          updated_at?: string;
          created_by?: string;
          updated_by?: string;
        };
      };
    };
  };
}
